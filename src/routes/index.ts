/* eslint-disable no-unused-vars */

export enum Routes {
  LOGIN = '/login',
  FORGOT_PASSWORD = '/forgot-password',
  RESET_PASSWORD = '/reset-password',
  ONBOARDING = '/onboarding',
  DASHBOARD = '/dashboard',
  TRANSACTIONS = '/dashboard/transactions',
  STATISTICS = '/dashboard/statistics',
  ACCOUNTS = '/dashboard/accounts',
  FORECASTING = '/dashboard/forecasting',
  MONITORING = '/dashboard/monitoring',
  CATEGORIES = '/dashboard/categories',
  SETTINGS = '/dashboard/settings',
  PRICING = '/pricing',
  LOGOUT = '/api/logout',
}

// Extract routes from Routes enum that don't need authentication and user token being present (used in middleware.ts)
export const NON_AUTH_ROUTES = [Routes.LOGIN, Routes.FORGOT_PASSWORD, Routes.RESET_PASSWORD, Routes.ONBOARDING];
