import { type NextRequest, NextResponse } from 'next/server';

/* 
  NextJS middleware uses Edge runtime which contains limited set of APIs https://nextjs.org/docs/pages/api-reference/edge
  When importing use absolute path to avoid build errors.
*/
import { cookieKeys } from '@/constants/api';
import { NON_AUTH_ROUTES, Routes } from '@/routes';

const AUTH_ROUTES = Object.values(Routes).filter((route) => !NON_AUTH_ROUTES.includes(route));
const { authToken } = cookieKeys;

export function middleware(request: NextRequest) {
  const token = request.cookies.get(authToken)?.value;

  const pathname = request.nextUrl.pathname;
  const isAuthRoute = AUTH_ROUTES.some((route) => pathname.startsWith(route));

  if (!token) {
    if (isAuthRoute) return NextResponse.redirect(new URL(Routes.LOGIN, request.url));
    if (pathname.startsWith(Routes.RESET_PASSWORD)) {
      if (!request.nextUrl.searchParams.get('token')) {
        return NextResponse.redirect(new URL(Routes.LOGIN, request.url));
      }
    }
    return NextResponse.next();
  }

  if (
    pathname.startsWith(Routes.LOGIN) ||
    pathname.startsWith(Routes.RESET_PASSWORD) ||
    pathname.startsWith(Routes.FORGOT_PASSWORD) ||
    pathname.startsWith(Routes.ONBOARDING)
  ) {
    return NextResponse.redirect(new URL(Routes.DASHBOARD, request.url));
  }

  return NextResponse.next();
}
