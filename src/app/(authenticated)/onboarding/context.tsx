'use client';

import {
  createContext,
  createRef,
  type Dispatch,
  type PropsWithChildren,
  type RefObject,
  type SetStateAction,
  useContext,
  useRef,
  useState,
} from 'react';
import type { TurnstileInstance } from '@marsidev/react-turnstile';

import { config } from '@/config';
import * as api from '@/services/apis';
import { useOAuthStore } from '@/store';
import type { IconType } from '@/ui/components';

import { OnboardingSteps } from './types';

export type OnboardingUser = Partial<api.RegistrationInput['user'] & { confirmPassword: string }>;

interface OnboardingContextValue {
  step: OnboardingSteps;
  setStep: (newStep: OnboardingSteps) => void;
  prevStep: OnboardingSteps | null;
  setPrevStep: Dispatch<SetStateAction<OnboardingSteps | null>>;
  user: OnboardingUser;
  setUser: Dispatch<SetStateAction<OnboardingUser>>;
  navigationOptions: {
    onNext?: () => void;
    onBack?: () => void;
    nextButtonText?: string;
    backDisabled?: boolean;
    nextLoading?: boolean;
    nextDisabled?: boolean;
    iconRight?: IconType;
  };
  setNavigationOptions: Dispatch<
    SetStateAction<{
      onNext?: (() => void) | undefined;
      onBack?: (() => void) | undefined;
      nextButtonText?: string;
      backDisabled?: boolean;
      nextLoading?: boolean;
      nextDisabled?: boolean;
      iconRight?: IconType;
    }>
  >;
  turnstileToken: string;
  setTurnstileToken: Dispatch<SetStateAction<string>>;
  turnstileRef: RefObject<TurnstileInstance>;
}

export const OnboardingContext = createContext<OnboardingContextValue>({
  step: OnboardingSteps.USER_INFO,
  setStep: () => null,
  prevStep: null,
  setPrevStep: () => null,
  user: {} as OnboardingUser,
  setUser: () => null,
  navigationOptions: {
    onNext: undefined,
    onBack: undefined,
    nextButtonText: undefined,
    backDisabled: false,
    nextLoading: false,
    nextDisabled: false,
    iconRight: undefined,
  },
  setNavigationOptions: () => null,
  turnstileToken: '',
  setTurnstileToken: () => null,
  turnstileRef: createRef<TurnstileInstance>(),
});

export const OnboardingContextProvider = ({ children }: PropsWithChildren) => {
  const { name, email, socialLoginProvider } = useOAuthStore();

  const [step, setStep] = useState<OnboardingSteps>(OnboardingSteps.USER_INFO);
  const [prevStep, setPrevStep] = useState<OnboardingSteps | null>(null);
  const [user, setUser] = useState<OnboardingUser>(socialLoginProvider ? { name, email, socialLoginProvider } : {});
  const [turnstileToken, setTurnstileToken] = useState(config.app.environment === 'local' ? 'mock' : '');
  const turnstileRef = useRef<TurnstileInstance>(null);

  const [navigationOptions, setNavigationOptions] = useState<{
    onNext?: (() => void) | undefined;
    onBack?: (() => void) | undefined;
    nextButtonText?: string;
    backDisabled?: boolean;
    nextLoading?: boolean;
    nextDisabled?: boolean;
    iconRight?: IconType;
  }>({
    onNext: undefined,
    onBack: undefined,
    nextButtonText: undefined,
    backDisabled: false,
    nextLoading: false,
    nextDisabled: false,
    iconRight: undefined,
  });

  function setStepAndPrevStep(newStep: OnboardingSteps) {
    setPrevStep(step);
    setStep(newStep);
  }

  return (
    <OnboardingContext.Provider
      value={{
        step,
        setStep: setStepAndPrevStep,
        prevStep,
        setPrevStep,
        user,
        setUser,
        navigationOptions,
        setNavigationOptions,
        turnstileToken,
        setTurnstileToken,
        turnstileRef,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboardingContext = () => {
  const context = useContext(OnboardingContext);

  if (!context) {
    throw new Error('useOnboardingContext must be used within a OnboardingContextProvider');
  }

  return context;
};
