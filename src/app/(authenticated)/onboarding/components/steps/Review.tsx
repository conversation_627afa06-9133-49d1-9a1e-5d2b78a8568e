// 'use client';

// import { useEffect } from 'react';
// import { useMutation } from '@tanstack/react-query';

// import { useOnboardingContext } from '@/app/(authenticated)/onboarding/context';
// import { config } from '@/config';
// import { useAuthContext } from '@/context';
// import { useBreakpoint, useCountries, useNavigation } from '@/hooks';
// import { Routes } from '@/routes';
// import * as api from '@/services/apis';
// import { ErrorSection, Loader, SingleDropdown, TextInput } from '@/ui/components';
// import { turnstileErrorHandler } from '@/utils';

// import { OnboardingSteps } from '../../types';
// import StepContainer from '../StepContainer';

// import { useCompanyRoleOptions, useCompanyTypeOptions, useIndustryGroupOptions } from './hooks';

// export default function Review() {
//   const { setStep, setNavigationOptions, user, company, turnstileToken, setTurnstileToken, turnstileRef } =
//     useOnboardingContext();
//   const { setAuthData } = useAuthContext();
//   const { router } = useNavigation();

//   const { isHMD, isCHsm, isCHxl } = useBreakpoint(undefined, { sm: 800, xl: 1200 });

//   const { companyRoleOptions, isCompanyRoleOptionsPending, isCompanyRoleOptionsError } = useCompanyRoleOptions();
//   const { industryOptions, isIndustryOptionsPending, isIndustryOptionsError } = useIndustryGroupOptions();
//   const { countries, countriesIsPending, countriesIsError } = useCountries();
//   const { companyTypeOptions, companyTypePending, companyTypeError } = useCompanyTypeOptions(company.countryId!);

//   const { mutate: register, isPending: isRegisterPending } = useMutation({
//     mutationFn: () => api.registration({ user, company } as api.RegistrationInput, turnstileToken),
//     onSuccess: (response) => {
//       setAuthData(response.data);
//       router.push(
//         response.redirectBaseUrl === config.app.baseUrl
//           ? Routes.ONBOARDING_SUCCESS
//           : `${response.redirectBaseUrl}${Routes.ONBOARDING_SUCCESS}`,
//       );
//     },
//     onError: (error) => turnstileErrorHandler(error, turnstileRef, setTurnstileToken),
//   });

//   useEffect(() => {
//     setNavigationOptions({
//       onNext: register,
//       onBack: () => setStep(OnboardingSteps.COMPANY_INFO),
//       nextDisabled: !turnstileToken,
//       nextLoading: isRegisterPending,
//       nextButtonText: 'Finish',
//     });
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [isRegisterPending, turnstileToken]);

//   useEffect(() => {
//     return () => {
//       setTurnstileToken(config.app.environment === 'local' ? 'mock' : '');
//     };
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   const isLoading = countriesIsPending || isIndustryOptionsPending || companyTypePending || isCompanyRoleOptionsPending;
//   const isError = countriesIsError || isIndustryOptionsError || companyTypeError || isCompanyRoleOptionsError;

//   return (
//     <StepContainer hasEnterAnimation={false} direction="normal">
//       {isError && <ErrorSection />}
//       {isLoading && <Loader />}
//       {!!countries && !!industryOptions && !!companyTypeOptions && (
//         <form className="flex max-w-fit grow gap-12 overflow-scroll hlg:flex-col hlg:gap-0">
//           <div className="flex w-[320px] flex-col hmd:w-[400px]">
//             <TextInput disabled value={company.name} label="Company Name" />

//             <TextInput disabled value={company.address} label="Company Address" />

//             <SingleDropdown
//               isDisabled
//               value={company.countryId}
//               options={countries?.map(({ id, name, flagEmoji }) => ({ value: id, label: `${flagEmoji} ${name}` }))}
//               label="Country"
//               className="mb-9"
//               maxMenuHeight={!isHMD ? 175 : 250}
//               menuPlacement={!isCHsm ? 'top' : 'bottom'}
//               isLoading={countriesIsPending}
//             />

//             <SingleDropdown
//               value={company.typeId}
//               options={companyTypeOptions?.map(({ id, name }) => ({ value: id, label: name }))}
//               label="Company Type"
//               className="mb-9"
//               isDisabled
//               maxMenuHeight={!isHMD ? 175 : 250}
//               menuPlacement={!isCHsm ? 'top' : 'bottom'}
//               isLoading={companyTypePending}
//             />
//           </div>

//           <div className="flex w-[320px] flex-col hmd:w-[400px]">
//             <SingleDropdown
//               value={company.industryId}
//               label="Company Industry"
//               isDisabled
//               className="mb-9"
//               options={industryOptions}
//               menuPlacement={!isHMD || isCHxl ? 'bottom' : 'top'}
//               maxMenuHeight={!isHMD ? 175 : 250}
//               isLoading={isIndustryOptionsPending}
//             />

//             <TextInput label="Full Name" disabled value={user.name} />

//             <SingleDropdown
//               name="companyRoleId"
//               label="Role in Organization"
//               value={user.companyRoleId}
//               options={companyRoleOptions}
//               showHeaderOptions={false}
//               className="mb-9"
//               isLoading={isCompanyRoleOptionsPending}
//               isDisabled
//             />

//             <TextInput label="Email" disabled value={user.email} />
//           </div>
//         </form>
//       )}
//     </StepContainer>
//   );
// }
